import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Logo from '../../assets/images/nuequiplogo.png';
import VerificationIcon from '../../assets/images/svg/VerificationIcon.svg';
import styles from './VerifyOtpPage.module.css';
import VerifyOtpForm from './components/VerifyOtpForm';

const VerifyOtpPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // Get email from navigation state
  const email = location.state?.email;

  // If no email is provided, redirect to registration
  React.useEffect(() => {
    if (!email) {
      navigate('/signup');
    }
  }, [email, navigate]);

  // Don't render if no email (will redirect)
  if (!email) {
    return null;
  }

  return (
    <div className={styles.verifyOtpContainer}>
      <img
        src={Logo}
        alt="Logo"
        style={{
          width: '200px',
          marginTop: '64px',
          marginLeft: '64px',
        }}
      />
      <div className={styles.headingContainer}>
        <div className={styles.iconContainer}>
          <img
            src={VerificationIcon}
            alt="Verification"
            className={styles.verificationIcon}
          />
        </div>
        <h2 className={styles.heading}>Verify Your Identity</h2>
        <p className={styles.subHeading}>
          We've sent an email with your code to:
          <br />
          <span className={styles.emailText}>{email}</span>
        </p>
      </div>
      <VerifyOtpForm email={email} />
    </div>
  );
};

export default VerifyOtpPage;
