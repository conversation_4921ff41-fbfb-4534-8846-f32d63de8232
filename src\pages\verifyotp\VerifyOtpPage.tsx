import React from 'react';
import { useLocation } from 'react-router-dom';
import Logo from '../../assets/images/nuequiplogo.png';
import VerificationIcon from '../../assets/images/svg/VerificationIcon.svg';
import styles from './VerifyOtpPage.module.css';
import VerifyOtpForm from './components/VerifyOtpForm';

const VerifyOtpPage: React.FC = () => {
  const location = useLocation();
  // Get email from navigation state or use default
  const email = location.state?.email || '<EMAIL>';

  return (
    <div className={styles.verifyOtpContainer}>
      <img
        src={Logo}
        alt="Logo"
        style={{
          width: '200px',
          marginTop: '64px',
          marginLeft: '64px',
        }}
      />
      <div className={styles.headingContainer}>
        <div className={styles.iconContainer}>
          <img
            src={VerificationIcon}
            alt="Verification"
            className={styles.verificationIcon}
          />
        </div>
        <h2 className={styles.heading}>Verify Your Identity</h2>
        <p className={styles.subHeading}>
          We've sent an email with your code to:
          <br />
          <span className={styles.emailText}>{email}</span>
        </p>
      </div>
      <VerifyOtpForm email={email} />
    </div>
  );
};

export default VerifyOtpPage;
