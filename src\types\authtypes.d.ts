export interface SignupResponse {
  timestamp: string;
  path: string;
  status: number;
  success: boolean;
  message: string;
  error: string;
  data: SignupData;
}

export interface SignupData {
  id: string;
  name: string;
  email: string;
  role: string;
  isEnabled: boolean;
}

export interface SignupCredentials {
  email: string;
  password: string;
  name: string;
  organization: string;
}

export interface SigninResponse {
  timestamp: string;
  path: string;
  status: number;
  success: boolean;
  message: string;
  error: string;
  data: Data;
}

export interface Data {
  token: string;
  user: User;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  isEnabled: boolean;
}

export interface SigninCredential {
  email: string;
  password: string;
}

// OTP API Response Types
export interface OTPVerifyResponse {
  timestamp: string;
  path: string;
  status: number;
  success: boolean;
  message: string;
  error: string;
  data: {
    token: string;
    user: {
      id: string;
      name: string;
      email: string;
      emailVerified: boolean;
      organization: string;
      role: string;
      profileImg: string;
      profileImgThumbnail: string;
      planId: string;
      tier: string;
      credits: number;
      tokens: number;
      createdOn: string;
      updatedOn: string;
      isEnabled: boolean;
    };
  };
}

export interface OTPResendResponse {
  timestamp: string;
  path: string;
  status: number;
  success: boolean;
  message: string;
  error: string | null;
  data: string;
}
