.verifyOtpContainer {
  position: fixed;
  background-color: white;
  background-image: url('../../assets/images/Login.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: right;
  overflow-y: auto;
  height: 100vh;
  width: 100vw;
}

.headingContainer {
  margin-top: 30px;
  margin-left: 132px;
  text-align: left;
  display: flex;
  flex-direction: column;
  width: 50%;
}

.iconContainer {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
}

.verificationIcon {
  width: 80px;
  height: 80px;
}

.heading {
  font-family: var(--font-family-primary);
  font-size: 48px;
  font-weight: var(--font-weight-semibold);
  color: #0e2f51;
  margin-bottom: 20px;
}

.subHeading {
  font-family: var(--font-family-primary);
  font-size: 16px;
  font-weight: var(--font-weight-normal);
  color: #667085;
  line-height: 1.5;
}

.emailText {
  font-weight: var(--font-weight-medium);
  color: #0e2f51;
}

.formContainer {
  width: 30%;
  margin-left: 132px;
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.otpInputContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.otpLabel {
  font-family: var(--font-family-primary);
  font-size: 14px;
  font-weight: var(--font-weight-medium);
  color: #d32f2f;
}

.otpInput {
  width: 100%;
  padding: 16px;
  border: 2px solid #d32f2f;
  border-radius: 8px;
  font-family: var(--font-family-primary);
  font-size: 18px;
  font-weight: var(--font-weight-medium);
  color: #0e2f51;
  background-color: #ffffff;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
  letter-spacing: 2px;
  text-align: center;
}

.otpInput:focus {
  outline: none;
  border-color: #1b5ea1;
}

.otpInputValid {
  border-color: #e0e0e0;
}

.errorMessage {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: var(--font-family-primary);
  font-size: 14px;
  color: #d32f2f;
  margin-top: 4px;
}

.errorIcon {
  width: 16px;
  height: 16px;
  background-color: #d32f2f;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.continueButton {
  width: 100%;
  padding: 16px;
  background-color: #1b5ea1;
  color: white;
  border: none;
  border-radius: 8px;
  font-family: var(--font-family-primary);
  font-size: 16px;
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 10px;
}

.continueButton:hover:not(:disabled) {
  background-color: #0e2f51;
}

.continueButton:disabled {
  background-color: #e0e0e0;
  color: #667085;
  cursor: not-allowed;
}

.linkContainer {
  font-family: var(--font-family-primary);
  font-size: 14px;
  color: #0e2f51;
  font-weight: var(--font-weight-medium);
  text-align: center;
  margin-top: 20px;
}

.link {
  color: #1b5ea1;
  font-size: 14px;
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  margin-left: 4px;
  cursor: pointer;
}

.link:hover {
  text-decoration: underline;
}

.goBackLink {
  color: #1b5ea1;
  font-size: 14px;
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  text-align: center;
  display: block;
  margin-top: 15px;
  cursor: pointer;
}

.goBackLink:hover {
  text-decoration: underline;
}

/* Force light theme for verify OTP page - completely override dark theme styles */
.verifyOtpContainer [data-theme='dark'] .otpInput,
.verifyOtpContainer .otpInput,
.verifyOtpContainer [data-theme='dark'] .force-light-theme .otpInput,
.verifyOtpContainer .force-light-theme .otpInput,
.verifyOtpContainer [data-force-light-theme='true'] .otpInput {
  background-color: #ffffff !important;
  border-color: #d32f2f !important;
  color: #0e2f51 !important;
}

.verifyOtpContainer [data-theme='dark'] .otpInputValid,
.verifyOtpContainer .otpInputValid,
.verifyOtpContainer [data-theme='dark'] .force-light-theme .otpInputValid,
.verifyOtpContainer .force-light-theme .otpInputValid,
.verifyOtpContainer [data-force-light-theme='true'] .otpInputValid {
  border-color: #e0e0e0 !important;
}

.verifyOtpContainer [data-theme='dark'] .otpInput:focus,
.verifyOtpContainer .otpInput:focus,
.verifyOtpContainer [data-theme='dark'] .force-light-theme .otpInput:focus,
.verifyOtpContainer .force-light-theme .otpInput:focus,
.verifyOtpContainer [data-force-light-theme='true'] .otpInput:focus {
  border-color: #1b5ea1 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .verifyOtpContainer {
    background-image: none;
  }

  .headingContainer {
    margin-left: 20px;
    margin-right: 20px;
    width: calc(100% - 40px);
    text-align: center;
  }

  .heading {
    font-size: 32px;
  }

  .formContainer {
    width: calc(100% - 40px);
    margin-left: 20px;
    margin-right: 20px;
  }

  .iconContainer {
    justify-content: center;
  }
}
