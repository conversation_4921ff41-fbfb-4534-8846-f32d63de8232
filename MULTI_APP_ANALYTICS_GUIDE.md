# Multi-App Google Analytics Identification Guide

## Overview

When you have multiple applications sending data to the same Google Analytics property, it's crucial to identify which app is generating which data. This guide explains how to distinguish your NEUQUIP Frontend app from other apps in your analytics.

## App Identification Setup

### Environment Variables
Your app now includes these identification variables in `.env`:

```bash
VITE_GA4_MEASUREMENT_ID=G-536DNL5JSS
VITE_APP_NAME=NEUQUIP-Frontend
VITE_APP_VERSION=1.0.0
```

### Automatic App Identification
Every analytics event now includes:
- `app_name`: "NEUQUIP-Frontend"
- `app_version`: "1.0.0" 
- `app_environment`: "development" or "production"

## How to Identify Your App Data in Google Analytics

### 1. **Real-time Reports**
- Go to **Reports** > **Real-time**
- Look for events with custom parameters:
  - `app_name = NEUQUIP-Frontend`
  - `app_environment = production` (for live site)

### 2. **Custom Dimensions (Recommended)**
Set up custom dimensions in GA4 to filter by app:

1. Go to **Configure** > **Custom definitions** > **Custom dimensions**
2. Create these custom dimensions:
   - **Dimension name**: App Name
   - **Parameter name**: `app_name`
   - **Scope**: Event
   
   - **Dimension name**: App Version  
   - **Parameter name**: `app_version`
   - **Scope**: Event
   
   - **Dimension name**: App Environment
   - **Parameter name**: `app_environment` 
   - **Scope**: Event

### 3. **Event Parameters**
In any GA4 report, you can:
- Click on any event
- View "Event parameters" 
- Look for `app_name`, `app_version`, `app_environment`

### 4. **Exploration Reports**
Create custom reports to filter by app:
1. Go to **Explore** > **Free form**
2. Add dimensions: Custom dimensions you created above
3. Add metrics: Events, Users, Sessions
4. Apply filters: `app_name = NEUQUIP-Frontend`

## Sample Collect Request Analysis

Your collect request now includes these parameters:
```
ep.app_name=NEUQUIP-Frontend
ep.app_version=1.0.0  
ep.app_environment=production
```

## Debugging App Identification

### Browser Console
In development mode, check the browser console for:
```
🔍 Google Analytics App Identification: {
  app_name: "NEUQUIP-Frontend",
  app_version: "1.0.0", 
  app_environment: "development",
  measurement_id: "G-536DNL5JSS"
}
```

### Network Tab
In browser DevTools > Network:
1. Filter for "collect" requests
2. Check request payload for app identification parameters

## Multiple Apps Setup Examples

If you have other apps, configure them differently:

**App 1 - NEUQUIP Frontend:**
```bash
VITE_APP_NAME=NEUQUIP-Frontend
VITE_APP_VERSION=1.0.0
```

**App 2 - NEUQUIP Admin:**
```bash
VITE_APP_NAME=NEUQUIP-Admin  
VITE_APP_VERSION=2.1.0
```

**App 3 - NEUQUIP Mobile:**
```bash
VITE_APP_NAME=NEUQUIP-Mobile
VITE_APP_VERSION=1.5.0
```

## Filtering Data by App

### In GA4 Interface:
1. **Audience Builder**: Create audiences based on `app_name`
2. **Comparison Reports**: Compare different apps side by side
3. **Segments**: Create segments for each app

### Common Filters:
- `app_name exactly matches NEUQUIP-Frontend`
- `app_environment exactly matches production`
- `app_version exactly matches 1.0.0`

## Benefits of App Identification

1. **Clear Data Separation**: Know which app generated which events
2. **Performance Comparison**: Compare metrics across different apps
3. **Debugging**: Quickly identify issues in specific apps
4. **User Journey**: Track users across multiple apps
5. **A/B Testing**: Compare different app versions

## Next Steps

1. **Deploy the updated code** to see app identification in action
2. **Set up custom dimensions** in GA4 for better filtering
3. **Create custom reports** filtered by your app
4. **Monitor real-time data** to verify app identification is working

## Verification Checklist

- [ ] Environment variables are set correctly
- [ ] Browser console shows app identification info (dev mode)
- [ ] Network requests include app parameters
- [ ] GA4 real-time reports show app_name parameter
- [ ] Custom dimensions are created in GA4
- [ ] Custom reports filter by app successfully

Your NEUQUIP Frontend app is now properly identified in Google Analytics!
