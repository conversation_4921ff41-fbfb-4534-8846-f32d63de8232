/**
 * Utility functions for user authentication and data management
 */

export interface UserData {
  id?: string;
  _id?: string;
  name: string;
  email: string;
  organization: string;
  profileImg?: string;
  profileImgThumbnail?: string;
  isEnabled?: boolean;
  [key: string]: any;
}

/**
 * Get current user data from localStorage
 */
export const getCurrentUser = (): UserData | null => {
  try {
    const userStr = localStorage.getItem('user');
    if (!userStr) return null;

    const user = JSON.parse(userStr);
    return user;
  } catch (error) {
    console.error('Error parsing user data from localStorage:', error);
    return null;
  }
};

/**
 * Get current user ID from localStorage
 * Handles both 'id' and '_id' fields for backward compatibility
 */
export const getCurrentUserId = (): string | null => {
  const user = getCurrentUser();
  if (!user) return null;

  // Try different possible ID fields
  return user.id || user._id || null;
};

/**
 * Get current user email from localStorage
 */
export const getCurrentUserEmail = (): string | null => {
  const user = getCurrentUser();
  return user?.email || null;
};

/**
 * Get current user name from localStorage
 */
export const getCurrentUserName = (): string | null => {
  const user = getCurrentUser();
  return user?.name || null;
};

/**
 * Check if user is authenticated
 */
export const isAuthenticated = (): boolean => {
  const token = localStorage.getItem('token');
  const user = getCurrentUser();
  return !!(token && user);
};

/**
 * Get authentication token
 */
export const getAuthToken = (): string | null => {
  try {
    const token = localStorage.getItem('token');
    return token ? token.replace(/"/g, '') : null;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

/**
 * Clear user authentication data
 */
export const clearAuthData = (): void => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
};

/**
 * Get current user tier/plan from localStorage
 */
export const getCurrentUserTier = (): string => {
  const user = getCurrentUser();
  return user?.tier || 'free' || 'Free';
};

/**
 * Check if current user is on free plan
 */
export const isUserOnFreePlan = (): boolean => {
  const tier = getCurrentUserTier();
  return tier === 'free' || tier === 'Free';
};

/**
 * Check if current user is on paid plan
 */
export const isUserOnPaidPlan = (): boolean => {
  return !isUserOnFreePlan();
};

/**
 * Update user data in localStorage
 */
export const updateUserData = (userData: Partial<UserData>): void => {
  try {
    const currentUser = getCurrentUser();
    if (currentUser) {
      const updatedUser = { ...currentUser, ...userData };
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
  } catch (error) {
    console.error('Error updating user data:', error);
  }
};
