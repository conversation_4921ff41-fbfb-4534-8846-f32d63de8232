import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import toast from 'react-hot-toast';
import {
  useVerifyOTPMutation,
  useReSentOTPMutation,
} from '../../../services/userMgtService';
import { setAuth } from '../../../store/authSlice';
import useLocalStorage from '../../../hooks/useLocalStorage';
import { getErrorMessage } from '../../../utils/errorHandler';
import { BASE_URL } from '../../../services/config';
import { useAnalyticsEvents } from '../../../hooks/useAnalytics';
import styles from '../VerifyOtpPage.module.css';

interface VerifyOtpFormProps {
  email: string;
}

const VerifyOtpForm: React.FC<VerifyOtpFormProps> = ({ email }) => {
  const [otpCode, setOtpCode] = useState('');
  const [hasError, setHasError] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [, setToken] = useLocalStorage('token', null);
  const [, setUser] = useLocalStorage('user', null);
  const { trackLogin, trackJSError, trackAPIError, trackButtonClick } =
    useAnalyticsEvents();

  const [verifyOTP, { isLoading: isVerifying }] = useVerifyOTPMutation();
  const [resendOTP, { isLoading: isResending }] = useReSentOTPMutation();

  const handleOtpChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, ''); // Only allow digits
    if (value.length <= 6) {
      setOtpCode(value);
      setHasError(false); // Clear error when user starts typing
    }
  };

  const handleVerifyOtp = async () => {
    if (otpCode.length !== 6) {
      setHasError(true);
      return;
    }

    try {
      const response = await verifyOTP({ email, otp: otpCode }).unwrap();

      // Handle successful verification
      if (response.success && response.data) {
        const { token, user } = response.data;

        // Process user data with profile images
        const newUserData = {
          ...user,
          profileImgThumbnail: user.profileImg
            ? `${BASE_URL}${user.profileImgThumbnail}`
            : null,
          profileImg: user.profileImg ? `${BASE_URL}${user.profileImg}` : null,
        };

        // Store authentication data
        setToken(token);
        setUser(newUserData);
        dispatch(
          setAuth({
            token: token,
            userDetails: newUserData,
          })
        );

        toast.success('OTP verified successfully!');

        // Track successful OTP verification
        trackLogin('otp');

        navigate('/'); // Redirect to dashboard
      } else {
        setHasError(true);
        toast.error('Invalid OTP code. Please try again.');
      }
    } catch (error: any) {
      setHasError(true);
      const errorMessage = getErrorMessage(error);
      toast.error(errorMessage || 'Failed to verify OTP. Please try again.');

      // Track OTP verification error
      trackAPIError(
        '/api/v1/auth/email-otp/verify',
        error?.status || 400,
        errorMessage
      );
    }
  };

  const handleResendCode = async () => {
    try {
      const response = await resendOTP({ email }).unwrap();

      if (response.success) {
        toast.success('Verification code sent successfully!');
        setOtpCode(''); // Clear the current OTP input
        setHasError(false); // Clear any existing errors

        // Track successful OTP resend
        trackButtonClick('otp_resend_success');
      } else {
        toast.error('Failed to resend code. Please try again.');
      }
    } catch (error: any) {
      const errorMessage = getErrorMessage(error);
      toast.error(errorMessage || 'Failed to resend code. Please try again.');

      // Track OTP resend error
      trackAPIError(
        '/api/v1/auth/email-otp/send',
        error?.status || 400,
        errorMessage
      );
    }
  };

  const handleGoBack = () => {
    navigate(-1); // Go back to previous page
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && otpCode.length === 6 && !isVerifying) {
      handleVerifyOtp();
    }
  };

  return (
    <div className={styles.formContainer}>
      <div className={styles.otpInputContainer}>
        <label className={styles.otpLabel}>Enter the 6-digit code*</label>
        <input
          type="text"
          value={otpCode}
          onChange={handleOtpChange}
          onKeyPress={handleKeyPress}
          placeholder="631514"
          className={`${styles.otpInput} ${!hasError ? styles.otpInputValid : ''}`}
          maxLength={6}
          disabled={isVerifying}
        />
        {hasError && (
          <div className={styles.errorMessage}>
            <span className={styles.errorIcon}>!</span>
            The code you entered is invalid.
          </div>
        )}
      </div>

      <button
        className={styles.continueButton}
        onClick={handleVerifyOtp}
        disabled={isVerifying || otpCode.length !== 6}
      >
        {isVerifying ? 'Verifying...' : 'Continue'}
      </button>

      <div className={styles.linkContainer}>
        Didn't receive a code?{' '}
        <span
          className={styles.link}
          onClick={handleResendCode}
          style={{
            opacity: isResending ? 0.6 : 1,
            pointerEvents: isResending ? 'none' : 'auto',
          }}
        >
          {isResending ? 'Sending...' : 'Resend'}
        </span>
      </div>

      <span className={styles.goBackLink} onClick={handleGoBack}>
        Go back
      </span>
    </div>
  );
};

export default VerifyOtpForm;
