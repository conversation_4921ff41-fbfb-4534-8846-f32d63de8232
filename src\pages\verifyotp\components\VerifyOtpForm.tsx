import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import styles from '../VerifyOtpPage.module.css';

interface VerifyOtpFormProps {
  email: string;
}

const VerifyOtpForm: React.FC<VerifyOtpFormProps> = ({ email }) => {
  const [otpCode, setOtpCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);
  const navigate = useNavigate();

  const handleOtpChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, ''); // Only allow digits
    if (value.length <= 6) {
      setOtpCode(value);
      setHasError(false); // Clear error when user starts typing
    }
  };

  const handleVerifyOtp = async () => {
    if (otpCode.length !== 6) {
      setHasError(true);
      return;
    }

    setIsLoading(true);

    try {
      // Simulate API call for OTP verification
      // Replace this with actual API call to your backend
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // For demo purposes, let's say any code except "123456" is invalid
      if (otpCode === '123456') {
        toast.success('OTP verified successfully!');
        navigate('/login'); // Redirect to login or dashboard
      } else {
        setHasError(true);
        toast.error('Invalid OTP code. Please try again.');
      }
    } catch (error) {
      setHasError(true);
      toast.error('Failed to verify OTP. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    try {
      // Simulate API call for resending OTP
      await new Promise((resolve) => setTimeout(resolve, 500));
      toast.success('Verification code sent successfully!');
    } catch (error) {
      toast.error('Failed to resend code. Please try again.');
    }
  };

  const handleGoBack = () => {
    navigate(-1); // Go back to previous page
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && otpCode.length === 6) {
      handleVerifyOtp();
    }
  };

  return (
    <div className={styles.formContainer}>
      <div className={styles.otpInputContainer}>
        <label className={styles.otpLabel}>Enter the 6-digit code*</label>
        <input
          type="text"
          value={otpCode}
          onChange={handleOtpChange}
          onKeyPress={handleKeyPress}
          placeholder="631514"
          className={`${styles.otpInput} ${!hasError ? styles.otpInputValid : ''}`}
          maxLength={6}
          disabled={isLoading}
        />
        {hasError && (
          <div className={styles.errorMessage}>
            <span className={styles.errorIcon}>!</span>
            The code you entered is invalid.
          </div>
        )}
      </div>

      <button
        className={styles.continueButton}
        onClick={handleVerifyOtp}
        disabled={isLoading || otpCode.length !== 6}
      >
        {isLoading ? 'Verifying...' : 'Continue'}
      </button>

      <div className={styles.linkContainer}>
        Didn't receive a code?{' '}
        <span className={styles.link} onClick={handleResendCode}>
          Resend
        </span>
      </div>

      <span className={styles.goBackLink} onClick={handleGoBack}>
        Go back
      </span>
    </div>
  );
};

export default VerifyOtpForm;
